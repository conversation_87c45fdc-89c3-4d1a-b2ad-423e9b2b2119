"""
测试不同筛查间隔的效果差异
验证修改后的模型能正确体现筛查保护效应和季度模拟
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.ccsm.core.model import ColorectalCancerMicrosimulationModel
from src.ccsm.modules.screening import ScreeningStrategy
from src.ccsm.core.enums import ScreeningTool
import pandas as pd


def test_screening_intervals():
    """测试不同筛查间隔的效果"""
    print("=== 测试不同筛查间隔的效果差异 ===\n")

    # 1. 创建模型实例
    print("1. 创建模型实例...")
    model = ColorectalCancerMicrosimulationModel(initial_population=5000)

    # 2. 设置人口分布
    print("2. 设置人口分布...")
    age_distribution = {
        50: 0.25,  # 50岁：25%
        55: 0.25,  # 55岁：25%
        60: 0.25,  # 60岁：25%
        65: 0.25   # 65岁：25%
    }
    gender_ratio = 0.5  # 男女比例1:1

    model.setup_population(age_distribution, gender_ratio)

    # 3. 定义要比较的筛查策略
    strategies_to_test = [
        "annual_fit",      # 年度FIT筛查
        "biennial_fit",    # 双年度FIT筛查
        "fit_1_5_years",   # 1.5年FIT筛查
        "fit_6_months"     # 6个月FIT筛查
    ]

    print(f"3. 测试策略: {strategies_to_test}")

    # 4. 运行单个策略并收集结果
    print("4. 运行各个策略...")
    results_summary = []

    for strategy_name in strategies_to_test:
        print(f"\n运行策略: {strategy_name}")
        try:
            # 重新设置人口以确保每个策略都从相同的初始状态开始
            model.setup_population(age_distribution, gender_ratio)

            # 运行单个策略
            result = model.run_simulation(
                years=10,
                screening_strategy=strategy_name,
                show_progress=False
            )

            # 提取关键指标
            economics = result.get('economics_outcome', {})
            final_disease_stats = result.get('final_disease_stats', {})

            summary = {
                'Strategy': strategy_name,
                'Total_Cost': economics.get('total_cost', 0),
                'Cancer_Cases_Prevented': economics.get('cancer_cases_prevented', 0),
                'QALYs_Gained': economics.get('qalys_gained', 0),
                'Cost_per_QALY': economics.get('cost_per_qaly_gained', 0),
                'Cancer_Incidence': final_disease_stats.get('cancer_incidence', 0),
                'Cancer_Deaths': final_disease_stats.get('cancer_deaths', 0)
            }
            results_summary.append(summary)
            print(f"✅ {strategy_name} 完成")

        except Exception as e:
            print(f"❌ 策略 {strategy_name} 运行失败: {e}")

    # 5. 分析结果
    print("\n=== 结果分析 ===")

    # 6. 显示结果表格
    if results_summary:
        df = pd.DataFrame(results_summary)
        print("\n策略比较结果:")
        print("=" * 100)
        print(f"{'策略名称':<15} {'总成本':<12} {'预防病例':<10} {'QALYs':<8} {'成本/QALY':<12} {'癌症发病':<10} {'癌症死亡':<10}")
        print("-" * 100)
        
        for _, row in df.iterrows():
            print(f"{row['Strategy']:<15} "
                  f"¥{row['Total_Cost']:<11,.0f} "
                  f"{row['Cancer_Cases_Prevented']:<10.0f} "
                  f"{row['QALYs_Gained']:<8.2f} "
                  f"¥{row['Cost_per_QALY']:<11,.0f} "
                  f"{row['Cancer_Incidence']:<10.0f} "
                  f"{row['Cancer_Deaths']:<10.0f}")

        # 7. 分析差异
        print("\n=== 差异分析 ===")
        
        # 按筛查频率排序
        df_sorted = df.sort_values('Cost_per_QALY')
        
        print("按成本效益排序:")
        for i, (_, row) in enumerate(df_sorted.iterrows(), 1):
            print(f"{i}. {row['Strategy']}: ¥{row['Cost_per_QALY']:,.0f}/QALY")
        
        # 检查是否有明显差异
        cancer_incidence_range = df['Cancer_Incidence'].max() - df['Cancer_Incidence'].min()
        cost_range = df['Total_Cost'].max() - df['Total_Cost'].min()
        
        print(f"\n差异检查:")
        print(f"癌症发病率差异: {cancer_incidence_range:.0f} 例")
        print(f"总成本差异: ¥{cost_range:,.0f}")
        
        if cancer_incidence_range > 0:
            print("✅ 不同筛查间隔产生了明显的癌症发病率差异")
        else:
            print("❌ 不同筛查间隔的癌症发病率相同，可能存在问题")
            
        if cost_range > 0:
            print("✅ 不同筛查间隔产生了明显的成本差异")
        else:
            print("❌ 不同筛查间隔的成本相同，可能存在问题")

    else:
        print("❌ 没有成功的测试结果")

    print("\n=== 测试完成 ===")


def test_protection_effect():
    """测试筛查保护效应"""
    print("\n=== 测试筛查保护效应 ===")
    
    # 创建简单的测试个体
    from src.ccsm.core.individual import Individual
    from src.ccsm.core.enums import Gender, CancerStage
    from src.ccsm.modules.disease import DiseaseNaturalHistoryModule, DiseaseParameters
    
    # 创建疾病模块
    disease_params = DiseaseParameters()
    disease_module = DiseaseNaturalHistoryModule(disease_params)
    
    # 创建两个相同的个体
    individual_screened = Individual(
        id=1,
        gender=Gender.MALE,
        birth_year=1970,
        current_age=50,
        last_screening_year=2020  # 最近筛查
    )
    
    individual_unscreened = Individual(
        id=2,
        gender=Gender.MALE,
        birth_year=1970,
        current_age=50,
        last_screening_year=None  # 从未筛查
    )
    
    # 测试腺瘤产生概率
    screened_prob = disease_module._should_generate_adenoma(individual_screened, 2021)
    unscreened_prob = disease_module._should_generate_adenoma(individual_unscreened, 2021)
    
    print(f"筛查个体腺瘤产生: {screened_prob}")
    print(f"未筛查个体腺瘤产生: {unscreened_prob}")
    
    # 多次测试以获得概率估计
    screened_count = 0
    unscreened_count = 0
    test_runs = 1000
    
    for _ in range(test_runs):
        if disease_module._should_generate_adenoma(individual_screened, 2021):
            screened_count += 1
        if disease_module._should_generate_adenoma(individual_unscreened, 2021):
            unscreened_count += 1
    
    screened_rate = screened_count / test_runs
    unscreened_rate = unscreened_count / test_runs
    
    print(f"\n{test_runs}次测试结果:")
    print(f"筛查个体腺瘤产生率: {screened_rate:.3f}")
    print(f"未筛查个体腺瘤产生率: {unscreened_rate:.3f}")
    print(f"保护效应: {(1 - screened_rate/unscreened_rate)*100:.1f}%")
    
    if screened_rate < unscreened_rate:
        print("✅ 筛查保护效应正常工作")
    else:
        print("❌ 筛查保护效应未生效")


if __name__ == '__main__':
    try:
        test_protection_effect()
        test_screening_intervals()
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
